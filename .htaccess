# Enable mod_rewrite
RewriteEngine On

# Serve .html files for clean URLs (e.g. /contact-us → contact-us.html)
RewriteCond %{REQUEST_FILENAME}.html -f
RewriteRule ^([^/]+)/?$ $1.html [L]

# Support nested routes (e.g. /project/avacasa → project/avacasa.html)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.html -f
RewriteRule ^(.*)/?$ $1.html [L]

# Set custom 404 page
ErrorDocument 404 /404.html
